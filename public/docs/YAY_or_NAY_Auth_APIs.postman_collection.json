{"info": {"_postman_id": "yay-nay-auth-apis", "name": "YAY or NAY - Authentication APIs", "description": "Complete authentication API collection for YAY or NAY app including registration, login, social auth, password management, and profile operations.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Public APIs", "item": [{"name": "User Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/register", "host": ["{{base_url}}"], "path": ["api", "register"]}, "description": "Register a new user with email and password. Username will be null initially and can be set later via profile update."}}, {"name": "User Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}, "description": "Login with email and password. Only email-based login is supported."}}, {"name": "Social Authentication - Google", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"provider\": \"google\",\n    \"access_token\": \"ya29.a0AfH6SMC...\"\n}"}, "url": {"raw": "{{base_url}}/api/social-auth", "host": ["{{base_url}}"], "path": ["api", "social-auth"]}, "description": "Authenticate using Google OAuth access token."}}, {"name": "Social Authentication - Apple", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"provider\": \"apple\",\n    \"access_token\": \"apple_access_token_here\"\n}"}, "url": {"raw": "{{base_url}}/api/social-auth", "host": ["{{base_url}}"], "path": ["api", "social-auth"]}, "description": "Authenticate using Apple Sign In access token."}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/forgot-password", "host": ["{{base_url}}"], "path": ["api", "forgot-password"]}, "description": "Send password reset email to the user."}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"token\": \"reset_token_from_email\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"newpassword123\",\n    \"password_confirmation\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/reset-password", "host": ["{{base_url}}"], "path": ["api", "reset-password"]}, "description": "Reset password using token received via email."}}], "description": "Public authentication endpoints that don't require authentication."}, {"name": "Protected APIs", "item": [{"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/logout", "host": ["{{base_url}}"], "path": ["api", "logout"]}, "description": "Logout the authenticated user and revoke the access token."}}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/profile", "host": ["{{base_url}}"], "path": ["api", "profile"]}, "description": "Get the authenticated user's profile information."}}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"username\": \"johnsmith\",\n    \"email\": \"<EMAIL>\",\n    \"age\": \"25-34\",\n    \"location\": \"New York, USA\"\n}"}, "url": {"raw": "{{base_url}}/api/profile", "host": ["{{base_url}}"], "path": ["api", "profile"]}, "description": "Update the authenticated user's profile information."}}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"oldpassword123\",\n    \"password\": \"newpassword123\",\n    \"password_confirmation\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/change-password", "host": ["{{base_url}}"], "path": ["api", "change-password"]}, "description": "Change the authenticated user's password."}}], "description": "Protected endpoints that require authentication via Bear<PERSON> token."}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "access_token", "value": "", "type": "string", "description": "Access token obtained from login or registration"}]}