## **YAY or NAY app proposal**

**YAY or NAY** is an engaging mobile application concept centered around interactive voting on business and user-submitted topics. The app enables discovery, discussion, and decision-making through a sleek swiping interface while offering a social layer for community engagement and trend tracking.

---

## **Core Modules & Features**

### **1\. Authentication & Account Management**

* **Signup/Login:** Via email, username, Google, and Apple  
* **Password Management:** Reset via secure email link  
* **Security:** Token-based sessions  
* **Invitations:** Shareable app link (Only frontend level)  
  * For an iOS device, it will open the App Store  
  * For an Android device, it will open the Play Store  
* **Privacy/Support:** Basic settings and help  
* **Logout Functionality**

### **2\. Explore Tab**

* **Search Bar:** Real-time filtering by topic/category  
* **Results View:** Tap to view details and vote (YAY/NAY/Skip)  
* **Featured Categories:** Horizontal scroll (Criteria for selecting the featured category?)  
* **Discover Hub & Grid View:** Browse all categories (Is this including all the topics from all categories, or have to implement random selection?)  
* **Navigation:** Seamless access to Category Detail View

\#Question

1. What is the timer logic?  
2. What is the business logic for Questions within the topic?

### **3\. Voting System**

* **Swipe-Based Interface:**  
  * Swipe up \= YAY  
  * Swipe down \= NAY  
  * Tap to skip  
* **Visual Feedback:** Green (YAY) / Red (NAY) background transitions  
* **Card UI:** Intuitive, dynamic vote presentation

### 

### **4\. My Votes Tab**

* **Vote Filters:** By type (YAY/NAY)  
* **Sorting Options:** Date, Category, Name  
* **Search & Stats:** Detailed vote history, statistics  
* **Topic Details:** Review past decisions and subquestions

### **5\. Hot Votes Tab**

* **Trending Votes:** Top topics in a 24-hour window  
* **All-Time Best:** Highest-rated content  
* **Ranking View:** Top categories with vote counts  
* **Navigation:** Quick access to full topic details

### **6\. TopicBox (User Submission)**

* **Form with Validation**  
* **Category Dropdown:** Controlled from master list  
* **Daily Limit:** Max 5 submissions  
* **Media Support:** Image from gallery or media URL  
* **Tags & Descriptions**  
* **User Voting:** On community-submitted topics  
* **Sort Options:** Newest / Most Popular

### **7\. Social Features**

* **Comment System:** Real-time add/delete  
* **Sharing Tools:** Easy share buttons  
* **Vote Stats:** Publicly visible  
* **Trending Areas:** Display hot categories and topics

### **8\. User Profile**

* **Profile Management:** Avatar upload, info edit  
* **Voting Stats:** Total, last vote time  
* **Interest Tags:** User preferences (categories)  
* **Edit Profile:** Name, age, gender, location, interests  
* **Password Change**

### 

### **9\. Notifications System**

* **Alerts for:**  
  * User-submitted topics  
  * Comments and replies  
  * Vote outcomes  
  * Voting reminders

### **10\. Backend & Admin Tools**

* **Admin Dashboard:** User management, topic moderation  
* **Analytics:** Vote breakdowns by category/topic  
* **Subquestion Insights**  
* **Notification System:** Backend-dispatched reminders and updates

---

## **Tech Stack**

* **Frontend:** React Native (iOS & Android)  
* **Backend:** Laravel 12  
* **Database:** PostgreSQL  
* **Auth:** JWT  
* **Notifications:** Firebase Cloud Messaging  
* **Media Storage:** Azure Storage

---

## **Project Timeline (40 Days)**

The YAY or NAY app will be fully developed and published to both app stores within **40 days**, divided into the following phases:

* **Days 1–10:** Setup, Authentication, Explore Tab  
* **Days 11–20:** Voting System, My Votes, Hot Votes  
* **Days 21–30:** TopicBox, Social Features, User Profile, Notifications  
* **Days 31–35:** Admin Panel, Testing, QA  
* **Days 36–40:** App Store Submission (iOS & Android), Final Launch Prep