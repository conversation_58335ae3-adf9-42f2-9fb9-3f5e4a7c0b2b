# YAY or NAY App Development Prompts

## Phase 1: Project Setup & Authentication (Days 1-3)

### 1.1 Clean Laravel Setup
- Remove Breeze web routes, keep API structure
- Install Laravel Sanctum for API authentication
- Configure CORS for mobile app requests
- Set up PostgreSQL database connection
- Configure Azure Storage for media files

### 1.2 Authentication APIs
- POST `/api/register` (email, username, Google, Apple)
- POST `/api/login` 
- POST `/api/logout`
- POST `/api/password/reset`
- GET `/api/user` (authenticated user profile)
- Implement JWT/Sanctum token management

## Phase 2: Core Models & Database (Days 4-6)

### 2.1 Database Schema
- Users (id, username, email, avatar, stats)
- Categories (id, name, featured, status)
- Topics (id, title, description, category_id, user_id, media_url, status, timer)
- Questions (id, topic_id, question_text, order)
- Votes (id, user_id, topic_id, vote_type, created_at)
- Comments (id, user_id, topic_id, content, created_at)
- UserSubmissions (id, user_id, topic_id, daily_count, created_at)

### 2.2 Model Relationships
- User hasMany Votes, Comments, Topics
- Topic belongsTo Category, User; hasMany Questions, Votes, Comments
- Category hasMany Topics

## Phase 3: Explore & Search APIs (Days 7-10)

### 3.1 Explore Endpoints
- GET `/api/categories` (featured categories)
- GET `/api/categories/{id}/topics` (topics by category)
- GET `/api/search/topics?q={query}` (real-time search)
- GET `/api/discover` (random topics grid)
- Implement caching for performance

### 3.2 Search Optimization
- Add database indexes for search fields
- Implement Laravel Scout for full-text search
- Cache popular searches and categories

## Phase 4: Voting System APIs (Days 11-14)

### 4.1 Voting Endpoints
- GET `/api/topics/{id}` (topic details with questions)
- POST `/api/topics/{id}/vote` (YAY/NAY/SKIP)
- GET `/api/topics/{id}/stats` (vote statistics)
- Implement vote validation and rate limiting

### 4.2 Vote Analytics
- Real-time vote counting
- Vote statistics aggregation
- Performance optimization for high-volume voting

## Phase 5: User Votes & History (Days 15-17)

### 5.1 Vote History APIs
- GET `/api/user/votes` (with filters: type, date, category)
- GET `/api/user/votes/stats` (user voting statistics)
- GET `/api/user/votes/search?q={query}`
- Implement pagination and sorting

## Phase 6: Hot Votes & Trending (Days 18-20)

### 6.1 Trending APIs
- GET `/api/trending/24h` (trending in 24 hours)
- GET `/api/trending/all-time` (highest-rated content)
- GET `/api/categories/ranking` (top categories with counts)
- Implement Redis caching for trending data

### 6.2 Performance Optimization
- Background jobs for trend calculations
- Efficient database queries with proper indexing
- Cache invalidation strategies

## Phase 7: TopicBox & User Submissions (Days 21-24)

### 7.1 Submission APIs
- POST `/api/topics/submit` (with validation, daily limit)
- GET `/api/topics/user-submitted` (community topics)
- POST `/api/topics/{id}/media` (image upload to Azure)
- Implement daily submission limits (5 per user)

### 7.2 Content Moderation
- Topic approval workflow
- Media validation and processing
- Spam prevention mechanisms

## Phase 8: Social Features (Days 25-28)

### 8.1 Comments & Social APIs
- POST `/api/topics/{id}/comments` (add comment)
- DELETE `/api/comments/{id}` (delete own comment)
- POST `/api/topics/{id}/share` (sharing functionality)
- GET `/api/topics/{id}/comments` (paginated comments)

### 8.2 Real-time Features
- WebSocket integration for live comments
- Real-time vote updates
- Push notification triggers

## Phase 9: User Profile & Settings (Days 29-31)

### 9.1 Profile APIs
- GET `/api/profile` (user profile with stats)
- PUT `/api/profile` (update profile info)
- POST `/api/profile/avatar` (avatar upload)
- PUT `/api/profile/password` (password change)
- PUT `/api/profile/interests` (category preferences)

## Phase 10: Notifications System (Days 32-34)

### 10.1 Notification APIs
- GET `/api/notifications` (user notifications)
- POST `/api/notifications/mark-read/{id}`
- Firebase Cloud Messaging integration
- Background job queues for notifications

### 10.2 Notification Types
- Topic submission alerts
- Comment notifications
- Vote outcome updates
- Voting reminders

## Phase 11: Admin Panel APIs (Days 35-37)

### 11.1 Admin Endpoints
- GET `/api/admin/users` (user management)
- GET `/api/admin/topics` (topic moderation)
- GET `/api/admin/analytics` (vote breakdowns)
- PUT `/api/admin/topics/{id}/status` (approve/reject)

## Phase 12: Performance & Security (Days 38-40)

### 12.1 Optimization
- Database query optimization
- API response caching
- Rate limiting implementation
- Security headers and validation

### 12.2 Scalability Features
- Database connection pooling
- Queue workers for background tasks
- CDN integration for media files
- Load balancing considerations

## Technical Requirements

### Database Design
- PostgreSQL with proper indexing
- Foreign key constraints
- Optimized queries for high-volume operations
- Connection pooling for scalability

### Security
- Bearer token authentication (Sanctum)
- Input validation and sanitization
- Rate limiting on all endpoints
- CORS configuration for mobile apps
- SQL injection prevention
- XSS protection

### Performance
- Redis caching for trending data
- Database query optimization
- Background job processing
- CDN for media delivery
- Efficient pagination
- API response compression

### Scalability
- Horizontal scaling considerations
- Queue workers for background tasks
- Database read replicas
- Load balancer ready architecture
- Microservice preparation

## API Response Standards

### Success Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Operation successful"
}
```

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": {}
  }
}
```

### Pagination Format
```json
{
  "success": true,
  "data": [],
  "pagination": {
    "current_page": 1,
    "per_page": 20,
    "total": 100,
    "last_page": 5
  }
}
```

## Development Guidelines

### Code Standards
- Follow PSR-12 coding standards
- Use Laravel best practices
- Implement repository pattern for data access
- Service layer for business logic
- Resource classes for API responses
- Form request classes for validation

### Testing Strategy
- Unit tests for models and services
- Feature tests for API endpoints
- Performance testing for high-load scenarios
- Security testing for authentication

### Documentation
- API documentation with examples
- Database schema documentation
- Deployment instructions
- Performance optimization guide

