#!/bin/bash

echo "🧪 Testing YAY or NAY API Docker Setup"
echo "====================================="
echo ""

# Configuration
BASE_URL="http://localhost:8000"
API_URL="$BASE_URL/api"
HEALTH_URL="$BASE_URL/health"
PHPMYADMIN_URL="http://localhost:8080"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test result
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ PASS${NC}: $2"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: $2"
        ((TESTS_FAILED++))
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    echo -e "${BLUE}Testing:${NC} $description"
    
    response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$url" 2>/dev/null)
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_result 0 "$description (HTTP $status_code)"
        return 0
    else
        print_result 1 "$description (Expected HTTP $expected_status, got $status_code)"
        return 1
    fi
}

# Function to test API endpoint with JSON
test_api_endpoint() {
    local url=$1
    local method=$2
    local data=$3
    local expected_status=$4
    local description=$5
    local auth_header=$6
    
    echo -e "${BLUE}Testing:${NC} $description"
    
    if [ -n "$auth_header" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            -H "Authorization: Bearer $auth_header" \
            -d "$data" \
            -o /tmp/api_response.json \
            "$url" 2>/dev/null)
    else
        response=$(curl -s -w "%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            -d "$data" \
            -o /tmp/api_response.json \
            "$url" 2>/dev/null)
    fi
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_result 0 "$description (HTTP $status_code)"
        return 0
    else
        print_result 1 "$description (Expected HTTP $expected_status, got $status_code)"
        echo -e "${YELLOW}Response:${NC} $(cat /tmp/api_response.json 2>/dev/null | head -c 200)..."
        return 1
    fi
}

echo "🔍 Step 1: Testing Basic Connectivity"
echo "------------------------------------"

# Test if containers are running
if ! docker ps | grep -q "yay-api"; then
    echo -e "${RED}❌ CRITICAL:${NC} YAY API container is not running!"
    echo "Please run: docker-compose up -d"
    exit 1
fi

if ! docker ps | grep -q "yay-mysql"; then
    echo -e "${RED}❌ CRITICAL:${NC} MySQL container is not running!"
    echo "Please run: docker-compose up -d"
    exit 1
fi

print_result 0 "Docker containers are running"

echo ""
echo "🌐 Step 2: Testing Web Endpoints"
echo "--------------------------------"

# Test home page
test_endpoint "$BASE_URL" "200" "Home page accessibility"

# Test health endpoint
test_endpoint "$HEALTH_URL" "200" "Health check endpoint"

# Test API info endpoint
test_endpoint "$BASE_URL/api-info" "200" "API info endpoint"

echo ""
echo "🔐 Step 3: Testing Authentication APIs"
echo "-------------------------------------"

# Generate unique test data
TIMESTAMP=$(date +%s)
TEST_EMAIL="test${TIMESTAMP}@example.com"
TEST_USERNAME="testuser${TIMESTAMP}"
TEST_PASSWORD="password123"

# Test user registration
REGISTER_DATA="{\"name\":\"Test User $TIMESTAMP\",\"email\":\"$TEST_EMAIL\",\"username\":\"$TEST_USERNAME\",\"password\":\"$TEST_PASSWORD\",\"password_confirmation\":\"$TEST_PASSWORD\"}"

if test_api_endpoint "$API_URL/register" "POST" "$REGISTER_DATA" "200" "User registration"; then
    # Extract token from response
    TOKEN=$(cat /tmp/api_response.json | grep -o '"token":"[^"]*' | cut -d'"' -f4)
    if [ -n "$TOKEN" ]; then
        print_result 0 "Registration token extraction"
    else
        print_result 1 "Registration token extraction"
    fi
else
    TOKEN=""
fi

# Test user login
LOGIN_DATA="{\"login\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}"
if test_api_endpoint "$API_URL/login" "POST" "$LOGIN_DATA" "200" "User login"; then
    # Extract token from response if registration failed
    if [ -z "$TOKEN" ]; then
        TOKEN=$(cat /tmp/api_response.json | grep -o '"token":"[^"]*' | cut -d'"' -f4)
    fi
fi

# Test validation errors
test_api_endpoint "$API_URL/register" "POST" "{}" "422" "Validation error handling"

echo ""
echo "🔒 Step 4: Testing Protected Endpoints"
echo "-------------------------------------"

if [ -n "$TOKEN" ]; then
    # Test profile endpoint
    test_api_endpoint "$API_URL/profile" "GET" "" "200" "Get user profile" "$TOKEN"
    
    # Test profile update
    UPDATE_DATA="{\"name\":\"Updated Test User $TIMESTAMP\"}"
    test_api_endpoint "$API_URL/profile" "PUT" "$UPDATE_DATA" "200" "Update user profile" "$TOKEN"
    
    # Test logout
    test_api_endpoint "$API_URL/logout" "POST" "" "200" "User logout" "$TOKEN"
else
    print_result 1 "Protected endpoints (no valid token available)"
fi

echo ""
echo "🗄️ Step 5: Testing Database Connectivity"
echo "---------------------------------------"

# Test database through health endpoint
if curl -s "$HEALTH_URL" | grep -q 'connected'; then
    print_result 0 "Database connectivity via health check"
else
    print_result 1 "Database connectivity via health check"
fi

echo ""
echo "🔧 Step 6: Testing Additional Services"
echo "-------------------------------------"

# Test phpMyAdmin
if test_endpoint "$PHPMYADMIN_URL" "200" "phpMyAdmin accessibility"; then
    echo -e "${GREEN}📊 phpMyAdmin is available at:${NC} $PHPMYADMIN_URL"
fi

# Test Postman collection download
if test_endpoint "http://localhost:8000/docs/YAY_or_NAY_Auth_APIs.postman_collection.json" "200" "Postman collection download"; then
    echo -e "${GREEN}📥 Postman collection is available for download${NC}"
fi

echo ""
echo "📊 Test Summary"
echo "==============="
echo -e "${GREEN}Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}Tests Failed: $TESTS_FAILED${NC}"
echo ""

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed! Your Docker setup is working correctly.${NC}"
    echo ""
    echo "📋 Quick Reference:"
    echo "• API Base URL: $BASE_URL"
    echo "• API Documentation: $BASE_URL (browser) or $BASE_URL/api-info (JSON)"
    echo "• Health Check: $HEALTH_URL"
    echo "• phpMyAdmin: $PHPMYADMIN_URL"
    echo "• Postman Collection: docs/YAY_or_NAY_Auth_APIs.postman_collection.json"
    echo ""
    echo "🚀 Your YAY or NAY API is ready to use!"
    exit 0
else
    echo -e "${RED}⚠️ Some tests failed. Please check the Docker setup.${NC}"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "• Check container logs: docker-compose logs app"
    echo "• Restart containers: docker-compose down && docker-compose up -d"
    echo "• Check container status: docker-compose ps"
    exit 1
fi
