# YAY or NAY API - Docker Setup

## One Command Installation

This Docker setup provides a complete, production-ready environment for the YAY or NAY API with all dependencies and configurations included.

### Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd yay-api

# Run the one-command setup
./setup.sh
```

That's it! The setup script will:
- ✅ Build and start all Docker containers
- ✅ Install all PHP dependencies
- ✅ Generate application key
- ✅ Run database migrations
- ✅ Install and configure Laravel Passport
- ✅ Set up OAuth clients
- ✅ Clear all caches
- ✅ Run comprehensive tests
- ✅ Provide you with all access URLs

## What's Included

### Services
- **API Application** (PHP 8.2 + Laravel 11 + Nginx)
- **MySQL 8.0** Database
- **Redis** Cache
- **phpMyAdmin** Database Management

### Ports
- **8000** - API Application
- **8080** - phpMyAdmin
- **3307** - MySQL (external access)
- **6380** - Redis (external access)

### Features
- ✅ Complete Laravel Passport OAuth2 setup
- ✅ Database migrations and seeding
- ✅ Redis caching
- ✅ Comprehensive API testing
- ✅ Production-ready configuration
- ✅ Automatic dependency management
- ✅ Health monitoring endpoints

## API Endpoints

### Public Endpoints
- `GET /` - API Documentation (HTML)
- `GET /api-info` - API Information (JSON)
- `GET /health` - Health Check
- `POST /api/register` - User Registration
- `POST /api/login` - User Login
- `POST /api/social-auth` - Social Authentication
- `POST /api/forgot-password` - Password Reset Request
- `POST /api/reset-password` - Password Reset

### Protected Endpoints (Require Authentication)
- `POST /api/logout` - User Logout
- `GET /api/profile` - Get User Profile
- `PUT /api/profile` - Update User Profile
- `POST /api/change-password` - Change Password

## Testing

### Automated Testing
The setup includes comprehensive automated testing:

```bash
# Run the test suite
./test-docker-setup.sh
```

### Manual Testing
```bash
# Test API health
curl http://localhost:8000/health

# Test user registration
curl -X POST http://localhost:8000/api/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","username":"testuser","password":"password123","password_confirmation":"password123"}'

# Test user login
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"login":"<EMAIL>","password":"password123"}'
```

### PHP Test Script
```bash
# Run the PHP test script
php docs/test_auth_api.php
```

## Management Commands

### Container Management
```bash
# View container status
docker-compose ps

# View application logs
docker-compose logs -f app

# Access container shell
docker-compose exec app bash

# Restart containers
docker-compose restart

# Stop containers
docker-compose down

# Stop and remove all data
docker-compose down -v
```

### Laravel Commands
```bash
# Run migrations
docker-compose exec app php artisan migrate

# Clear caches
docker-compose exec app php artisan cache:clear

# Generate new application key
docker-compose exec app php artisan key:generate

# Create Passport clients
docker-compose exec app php artisan passport:client --personal
```

## Environment Configuration

The setup uses `.env.docker` for Docker-specific configuration:
- Database host: `db` (container name)
- Redis host: `redis` (container name)
- App URL: `http://localhost:8000`

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 8000, 8080, 3307, 6380 are available
2. **Docker not running**: Start Docker Desktop/daemon
3. **Permission issues**: Run `chmod +x setup.sh test-docker-setup.sh`

### Logs and Debugging
```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs app
docker-compose logs db

# Check container health
docker-compose ps
```

### Reset Everything
```bash
# Complete reset (removes all data)
docker-compose down -v
docker system prune -f
./setup.sh
```

## Production Considerations

For production deployment:
1. Change default passwords in `docker-compose.yml`
2. Use environment-specific `.env` files
3. Enable HTTPS/SSL
4. Configure proper backup strategies
5. Set up monitoring and logging
6. Review security configurations

## Support

- **API Documentation**: http://localhost:8000
- **Postman Collection Download**: http://localhost:8000/docs/YAY_or_NAY_Auth_APIs.postman_collection.json
- **API Reference**: `docs/AUTHENTICATION_API.md`
- **Test Scripts**: `docs/test_auth_api.php`
