
# YAY or NAY API

A Laravel 11 REST API for the YAY or NAY mobile application with comprehensive authentication system and social login support.

## Features

- JWT-based authentication with configurable token expiration
- Social authentication (Google, Apple)
- User management with gamification (points, levels, streaks)
- Topic and voting system with categories
- Comment and question management
- Password reset functionality
- Rate limiting and input validation

## Installation

### Option 1: Docker (Recommended)

1. Clone the repository
2. Run setup script: `./docker-setup.sh`
3. Access API at `http://localhost:8000`

### Option 2: Manual Installation

1. Clone the repository
2. Install dependencies: `composer install`
3. Copy environment file: `cp .env.example .env`
4. Configure database credentials in `.env`
5. Generate application key: `php artisan key:generate`
6. Run migrations: `php artisan migrate`
7. Install Passport: `php artisan passport:install`

## Configuration

### Environment Variables

Configure the following in your `.env` file:

```env
# Database
DB_CONNECTION=mysql
DB_DATABASE=yay
DB_USERNAME=root
DB_PASSWORD=

# Passport Token Expiration (minutes, null = never expires)
PASSPORT_ACCESS_TOKEN_EXPIRE=1440
PASSPORT_REFRESH_TOKEN_EXPIRE=43200
PASSPORT_PERSONAL_ACCESS_TOKEN_EXPIRE=525600

# Social Authentication
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
APPLE_CLIENT_ID=your_apple_client_id
APPLE_CLIENT_SECRET=your_apple_client_secret
```


## API Endpoints

### Authentication
- `POST /api/register` - User registration
- `POST /api/login` - User login (email/username)
- `POST /api/social-auth` - Google/Apple authentication
- `POST /api/forgot-password` - Password reset request
- `POST /api/reset-password` - Password reset with token
- `POST /api/logout` - User logout (requires auth)
- `GET /api/profile` - Get user profile (requires auth)
- `PUT /api/profile` - Update user profile (requires auth)
- `POST /api/change-password` - Change password (requires auth)

### System
- `GET /` - API information and endpoints
- `GET /health` - Health check and system status

## Authentication

All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer {token}
```

Tokens are obtained from login, register, or social-auth endpoints.

## Docker

### Services
- **app**: PHP 8.2-FPM + Nginx (Port 8000)
- **db**: MySQL 8.0 (Port 3306)
- **redis**: Redis 7 (Port 6379)
- **phpmyadmin**: Database management (Port 8080)

### Commands
```bash
# Start containers
docker-compose up -d

# View logs
docker-compose logs app

# Access container shell
docker-compose exec app bash

# Stop containers
docker-compose down
```

## Development

For local development and testing, technical documentation and test scripts are available in the `docs/` folder (not committed to repository).

## Architecture

- Repository pattern for data access
- Service providers for configuration
- Custom exception handling
- Multilingual support
- Comprehensive input validation
- Rate limiting and security middleware
