#!/bin/bash

echo "🚀 Starting YAY or NAY API Docker Container..."

# Function to wait for database
wait_for_db() {
    echo "⏳ Waiting for database connection..."
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if php -r "
            try {
                \$pdo = new PDO('mysql:host=db;port=3306;dbname=yay', 'yay_user', 'password');
                echo 'Connected successfully';
                exit(0);
            } catch (PDOException \$e) {
                exit(1);
            }
        " > /dev/null 2>&1; then
            echo "✅ Database connection established!"
            return 0
        fi

        echo "Database not ready, attempt $attempt/$max_attempts, waiting 3 seconds..."
        sleep 3
        attempt=$((attempt + 1))
    done

    echo "❌ Failed to connect to database after $max_attempts attempts"
    return 1
}

# Function to check if setup is needed
needs_setup() {
    # Check if APP_KEY is set and passport keys exist
    if [ -z "$APP_KEY" ] || [ ! -f "/var/www/html/storage/oauth-private.key" ]; then
        return 0  # true - needs setup
    else
        return 1  # false - already setup
    fi
}

# Setup Laravel application
setup_laravel() {
    echo "🔧 Setting up Laravel application..."

    # Generate application key if not set
    if [ -z "$APP_KEY" ]; then
        echo "🔑 Generating application key..."
        php artisan key:generate --force
    fi

    # Wait for database
    wait_for_db

    # Run migrations
    echo "🗄️ Running database migrations..."
    php artisan migrate --force

    # Install Passport
    echo "🔐 Installing Laravel Passport..."
    php artisan passport:install --force

    # Run seeders (includes Passport client seeder)
    echo "🌱 Running database seeders..."
    php artisan db:seed --force

    # Clear caches
    echo "🧹 Clearing application caches..."
    php artisan config:clear
    php artisan cache:clear
    php artisan route:clear
    php artisan view:clear

    # Set proper permissions
    echo "📁 Setting proper permissions..."
    chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache
    chmod -R 775 /var/www/html/storage /var/www/html/bootstrap/cache

    echo "✅ Laravel application setup complete!"
}

# Main execution
cd /var/www/html

# Always run setup for fresh containers
if needs_setup; then
    setup_laravel
else
    echo "📋 Application already configured, skipping setup..."
    # Still wait for database and run migrations in case of updates
    wait_for_db
    php artisan migrate --force
    php artisan db:seed --force
fi

echo "🌐 Starting web services..."

# Start supervisor to manage PHP-FPM and Nginx
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
