<?php
namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller as Controller;
use App\Repositories\V1\AuthRepository;
use App\Utilities\ResponseHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Laravel\Socialite\Facades\Socialite;

class AuthController extends Controller
{
    protected AuthRepository $authRepository;

    /**
     * AuthController constructor.
     *
     * @param AuthRepository $authRepository
     */
    public function __construct(AuthRepository $authRepository, Request $request)
    {
        parent::__construct($request);
        $this->authRepository = $authRepository;

    }

    /**
     * Handle user registration.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ValidationException
     */
    public function register(Request $request): JsonResponse
    {
        $rules = [
            'name' => 'sometimes|string|max:255', // Optional name field
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6|confirmed',
        ];

        $validated = $this->validated($rules, $request->all());

        if ($validated->fails()) {
           return ResponseHandler::error(__('common.errors.validation'), 422, 12, $validated->errors());
        }

        return $this->authRepository->registerUser($validated->validated());
    }

    public function login(Request $request): JsonResponse
    {
        $rules = [
            'email' => 'required|email',
            'password' => 'required'
        ];

        $validated = $this->validated($rules, $request->all());

        if ($validated->fails()) {
            return ResponseHandler::error(__('common.errors.validation'), 422, 12, $validated->errors());
        }

        return $this->authRepository->loginUser($validated->validated());
    }

    public function logout(Request $request): JsonResponse
    {
        return $this->authRepository->logoutUser();
    }

    /**
     * Handle social authentication (Google/Apple)
     */
    public function socialAuth(Request $request): JsonResponse
    {
        $rules = [
            'provider' => 'required|in:google,apple',
            'access_token' => 'required|string',
        ];

        $validated = $this->validated($rules, $request->all());

        if ($validated->fails()) {
            return ResponseHandler::error(__('common.errors.validation'), 422, 12, $validated->errors());
        }

        return $this->authRepository->socialAuth($validated->validated());
    }

    /**
     * Send password reset email
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        $rules = [
            'email' => 'required|email|exists:users,email',
        ];

        $validated = $this->validated($rules, $request->all());

        if ($validated->fails()) {
            return ResponseHandler::error(__('common.errors.validation'), 422, 12, $validated->errors());
        }

        return $this->authRepository->sendPasswordResetEmail($validated->validated());
    }

    /**
     * Reset password with token
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $rules = [
            'token' => 'required|string',
            'email' => 'required|email',
            'password' => 'required|min:6|confirmed',
        ];

        $validated = $this->validated($rules, $request->all());

        if ($validated->fails()) {
            return ResponseHandler::error(__('common.errors.validation'), 422, 12, $validated->errors());
        }

        return $this->authRepository->resetPassword($validated->validated());
    }

    /**
     * Get authenticated user profile
     */
    public function profile(Request $request): JsonResponse
    {
        return $this->authRepository->getUserProfile();
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $rules = [
            'name' => 'sometimes|string|max:255',
            'username' => 'sometimes|string|max:255|unique:users,username,' . auth()->id(),
            'email' => 'sometimes|email|unique:users,email,' . auth()->id(),
            'age' => 'sometimes|string|max:50',
            'location' => 'sometimes|string|max:255',
            'avatar' => 'sometimes|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
        ];

        $validated = $this->validated($rules, array_merge($request->all(), $request->allFiles()));

        if ($validated->fails()) {
            return ResponseHandler::error(__('common.errors.validation'), 422, 12, $validated->errors());
        }

        return $this->authRepository->updateProfile($validated->validated());
    }

    /**
     * Change password
     */
    public function changePassword(Request $request): JsonResponse
    {
        $rules = [
            'current_password' => 'required',
            'password' => 'required|min:6|confirmed',
        ];

        $validated = $this->validated($rules, $request->all());

        if ($validated->fails()) {
            return ResponseHandler::error(__('common.errors.validation'), 422, 12, $validated->errors());
        }

        return $this->authRepository->changePassword($validated->validated());
    }
}
