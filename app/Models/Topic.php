<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Topic extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'category_id',
        'user_id',
        'media_url',
        'status',
        'timer',
        'vote_count',
    ];

    protected $casts = [
        'timer' => 'integer',
        'vote_count' => 'integer',
    ];

    /**
     * Get the category that owns the topic.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the user that owns the topic.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the questions for the topic.
     */
    public function questions()
    {
        return $this->hasMany(Question::class);
    }

    /**
     * Get the votes for the topic.
     */
    public function votes()
    {
        return $this->hasMany(Vote::class);
    }

    /**
     * Get the comments for the topic.
     */
    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Get the submissions for the topic.
     */
    public function submissions()
    {
        return $this->hasMany(UserSubmission::class);
    }
}
