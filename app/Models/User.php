<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Passport\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'username',
        'user_role',
        'age',
        'location',
        'avatar',
        'points',
        'level',
        'streak',
        'provider',
        'provider_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'points' => 'integer',
            'level' => 'integer',
            'streak' => 'integer',
            'user_role' => 'integer',
        ];
    }

    // Many-to-many with Projects
    public function projects()
    {
        return $this->belongsToMany(Project::class);
    }

    // One-to-many with Timesheets
    public function timesheets()
    {
        return $this->hasMany(Timesheets::class);
    }

    /**
     * Get the topics for the user.
     */
    public function topics()
    {
        return $this->hasMany(Topic::class);
    }

    /**
     * Get the votes for the user.
     */
    public function votes()
    {
        return $this->hasMany(Vote::class);
    }

    /**
     * Get the comments for the user.
     */
    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Get the submissions for the user.
     */
    public function submissions()
    {
        return $this->hasMany(UserSubmission::class);
    }

    /**
     * Get the role for the user.
     */
    public function role()
    {
        return $this->belongsTo(Role::class, 'user_role');
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole($role)
    {
        if (is_string($role)) {
            return $this->role->name === $role;
        }
        return $this->user_role === $role;
    }

    /**
     * Check if user is normal user
     */
    public function isNormal()
    {
        return $this->user_role === Role::NORMAL;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin()
    {
        return $this->user_role === Role::ADMIN;
    }

    /**
     * Check if user is super admin
     */
    public function isSuperAdmin()
    {
        return $this->user_role === Role::SUPER_ADMIN;
    }
}
