<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserSubmission extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'topic_id',
        'daily_count',
        'submission_date',
    ];

    protected $casts = [
        'daily_count' => 'integer',
        'submission_date' => 'date',
    ];

    /**
     * Get the user that owns the submission.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the topic that owns the submission.
     */
    public function topic()
    {
        return $this->belongsTo(Topic::class);
    }
}
