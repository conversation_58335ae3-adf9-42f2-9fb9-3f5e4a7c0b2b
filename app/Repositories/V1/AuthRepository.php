<?php

namespace App\Repositories\V1;

use App\Models\User;
use App\Models\Interest;
use App\Utilities\ResponseHandler;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;


class AuthRepository extends BaseRepository
{
    protected string $logChannel;

    protected $fileUploadService;

    public function __construct(Request $request, User $user, FileUploadService $fileUploadService)
    {
        parent::__construct($user);
        $this->logChannel = 'auth_logs';
        $this->fileUploadService = $fileUploadService;
    }

    public function registerUser(array $validatedRequest)
    {
        try {
            $user = $this->model::create([
                'name' => $validatedRequest['name'] ?? null, // Optional name, can be set later via profile update
                'email' => $validatedRequest['email'],
                'username' => null, // Keep username null for now
                'user_role' => 1, // Default to normal user role
                'age' => null, // Keep age null for now
                'location' => null, // Keep location null for now
                'password' => Hash::make($validatedRequest['password']),
                'points' => 0,
                'level' => 1,
                'streak' => 0,
            ]);

            // Issue access token
            $dataToReturn['token'] = $user->createToken('authToken')->accessToken;
            $dataToReturn['user'] = $user;
            $dataToReturn['interests'] = Interest::all(['id', 'name', 'slug', 'color']);
            return ResponseHandler::success($dataToReturn, __('common.success'));
        } catch (\Exception $e) {
            $this->logData($this->logChannel, $this->prepareExceptionLog($e), 'error');
            return ResponseHandler::error($this->prepareExceptionLog($e), 500,14);
        }
    }

    public function loginUser(array $validatedRequest)
    {
        try {
            // Login with email only
            $user = $this->model::where('email', $validatedRequest['email'])->first();

            if (!$user || !Hash::check($validatedRequest['password'], $user->password)) {
                return ResponseHandler::error(__('common.errors.invalidCreds'), 401);
            }

            $dataToReturn['token'] = $user->createToken('authToken')->accessToken;
            $dataToReturn['user'] = $user;
            $dataToReturn['interests'] = Interest::all(['id', 'name', 'slug', 'color']);
            return ResponseHandler::success($dataToReturn, __('common.success'));

        } catch (\Exception $e) {
            $this->logData($this->logChannel, $this->prepareExceptionLog($e), 'error');
            return ResponseHandler::error($this->prepareExceptionLog($e), 500,14);
        }
    }

    public function logoutUser()
    {
        try {
            auth()->guard('api')->user()->token()->revoke();
            return ResponseHandler::success([],__('common.success'));
        } catch (\Exception $e) {
            $this->logData($this->logChannel, $this->prepareExceptionLog($e), 'error');
            return ResponseHandler::error($this->prepareExceptionLog($e), 500,14);
        }
    }

    public function socialAuth(array $validatedRequest)
    {
        try {
            $provider = $validatedRequest['provider'];
            $accessToken = $validatedRequest['access_token'];

            // Get user info from social provider
            $socialUser = Socialite::driver($provider)->userFromToken($accessToken);

            // Check if user already exists
            $user = $this->model::where('email', $socialUser->getEmail())->first();

            if (!$user) {
                // Create new user
                $user = $this->model::create([
                    'name' => $socialUser->getName(),
                    'email' => $socialUser->getEmail(),
                    'username' => $this->generateUniqueUsername($socialUser->getName()),
                    'password' => Hash::make(Str::random(16)), // Random password for social users
                    'points' => 0,
                    'level' => 1,
                    'streak' => 0,
                    'provider' => $provider,
                    'provider_id' => $socialUser->getId(),
                ]);
            }

            $dataToReturn['token'] = $user->createToken('authToken')->accessToken;
            $dataToReturn['user'] = $user;
            return ResponseHandler::success($dataToReturn, __('common.success'));

        } catch (\Exception $e) {
            $this->logData($this->logChannel, $this->prepareExceptionLog($e), 'error');
            return ResponseHandler::error($this->prepareExceptionLog($e), 500, 14);
        }
    }

    public function sendPasswordResetEmail(array $validatedRequest)
    {
        try {
            $status = Password::sendResetLink(['email' => $validatedRequest['email']]);

            if ($status === Password::RESET_LINK_SENT) {
                return ResponseHandler::success([], __('passwords.sent'));
            }

            return ResponseHandler::error(__('passwords.user'), 400);
        } catch (\Exception $e) {
            $this->logData($this->logChannel, $this->prepareExceptionLog($e), 'error');
            return ResponseHandler::error($this->prepareExceptionLog($e), 500, 14);
        }
    }

    public function resetPassword(array $validatedRequest)
    {
        try {
            $status = Password::reset(
                $validatedRequest,
                function ($user, $password) {
                    $user->forceFill([
                        'password' => Hash::make($password)
                    ])->save();
                }
            );

            if ($status === Password::PASSWORD_RESET) {
                return ResponseHandler::success([], __('passwords.reset'));
            }

            return ResponseHandler::error(__('passwords.token'), 400);
        } catch (\Exception $e) {
            $this->logData($this->logChannel, $this->prepareExceptionLog($e), 'error');
            return ResponseHandler::error($this->prepareExceptionLog($e), 500, 14);
        }
    }

    public function getUserProfile()
    {
        try {
            $user = auth()->guard('api')->user();
            return ResponseHandler::success(['user' => $user], __('common.success'));
        } catch (\Exception $e) {
            $this->logData($this->logChannel, $this->prepareExceptionLog($e), 'error');
            return ResponseHandler::error($this->prepareExceptionLog($e), 500, 14);
        }
    }

    public function updateProfile(array $validatedRequest)
    {
        try {
            $user = auth()->guard('api')->user();

            // Handle avatar upload if present
            if (request()->hasFile('avatar')) {
                $avatarFile = request()->file('avatar');
                $avatarUrl = $this->fileUploadService->uploadAvatar($avatarFile, 'avatars', $user->avatar);

                if ($avatarUrl) {
                    $validatedRequest['avatar'] = $avatarUrl;
                } else {
                    return ResponseHandler::error('Failed to upload avatar', 500, 15);
                }
            }

            $user->update($validatedRequest);

            return ResponseHandler::success(['user' => $user->fresh()], __('common.success'));
        } catch (\Exception $e) {
            $this->logData($this->logChannel, $this->prepareExceptionLog($e), 'error');
            return ResponseHandler::error($this->prepareExceptionLog($e), 500, 14);
        }
    }

    public function changePassword(array $validatedRequest)
    {
        try {
            $user = auth()->guard('api')->user();

            if (!Hash::check($validatedRequest['current_password'], $user->password)) {
                return ResponseHandler::error(__('auth.password'), 400);
            }

            $user->update([
                'password' => Hash::make($validatedRequest['password'])
            ]);

            return ResponseHandler::success([], __('common.success'));
        } catch (\Exception $e) {
            $this->logData($this->logChannel, $this->prepareExceptionLog($e), 'error');
            return ResponseHandler::error($this->prepareExceptionLog($e), 500, 14);
        }
    }

    private function generateUniqueUsername($name)
    {
        $baseUsername = Str::slug($name, '');
        $username = $baseUsername;
        $counter = 1;

        while ($this->model::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }

}
