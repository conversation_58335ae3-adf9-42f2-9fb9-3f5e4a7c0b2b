<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Exception;

/**
 * File Upload Service
 *
 * Handles file uploads with automatic S3/local storage fallback
 *
 * S3 Configuration:
 * - Set AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, and AWS_BUCKET in .env to use S3
 * - Leave these empty to use local storage automatically
 * - Supports all standard AWS S3 configuration options
 */
class FileUploadService
{
    /**
     * Upload a file to S3 or local storage based on configuration
     *
     * @param UploadedFile $file
     * @param string $folder
     * @param string|null $oldFile
     * @return string|null
     */
    public function uploadAvatar(UploadedFile $file, string $folder = 'avatars', ?string $oldFile = null): ?string
    {
        try {
            // Validate file
            if (!$this->isValidImage($file)) {
                throw new Exception('Invalid image file');
            }

            // Delete old file if exists
            if ($oldFile) {
                $this->deleteFile($oldFile);
            }

            // Generate unique filename
            $filename = $this->generateFilename($file);
            $path = $folder . '/' . $filename;

            // Determine storage disk
            $disk = $this->getStorageDisk();

            // Upload file
            $uploaded = Storage::disk($disk)->putFileAs($folder, $file, $filename, 'public');

            if (!$uploaded) {
                throw new Exception('Failed to upload file');
            }

            // Return the full URL
            return $this->getFileUrl($path, $disk);

        } catch (Exception $e) {
            \Log::error('File upload failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Delete a file from storage
     *
     * @param string $filePath
     * @return bool
     */
    public function deleteFile(string $filePath): bool
    {
        try {
            $disk = $this->getStorageDisk();
            
            // Extract path from URL if it's a full URL
            $path = $this->extractPathFromUrl($filePath);
            
            if (Storage::disk($disk)->exists($path)) {
                return Storage::disk($disk)->delete($path);
            }
            
            return true; // File doesn't exist, consider it deleted
        } catch (Exception $e) {
            \Log::error('File deletion failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get the appropriate storage disk
     *
     * @return string
     */
    private function getStorageDisk(): string
    {
        // Check if S3 credentials are configured
        if ($this->isS3Configured()) {
            return 's3';
        }
        
        return 'public';
    }

    /**
     * Check if S3 is properly configured
     *
     * @return bool
     */
    private function isS3Configured(): bool
    {
        return !empty(config('filesystems.disks.s3.key')) &&
               !empty(config('filesystems.disks.s3.secret')) &&
               !empty(config('filesystems.disks.s3.bucket'));
    }

    /**
     * Validate if the uploaded file is a valid image
     *
     * @param UploadedFile $file
     * @return bool
     */
    private function isValidImage(UploadedFile $file): bool
    {
        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            return false;
        }

        // Check mime type
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            return false;
        }

        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions)) {
            return false;
        }

        return true;
    }

    /**
     * Generate a unique filename for the uploaded file
     *
     * @param UploadedFile $file
     * @return string
     */
    private function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        
        return "avatar_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Get the full URL for a file
     *
     * @param string $path
     * @param string $disk
     * @return string
     */
    private function getFileUrl(string $path, string $disk): string
    {
        if ($disk === 's3') {
            return Storage::disk('s3')->url($path);
        }
        
        // For local storage, return full URL
        return url('storage/' . $path);
    }

    /**
     * Extract file path from URL
     *
     * @param string $url
     * @return string
     */
    private function extractPathFromUrl(string $url): string
    {
        // If it's already a path, return as is
        if (!str_contains($url, 'http')) {
            return $url;
        }

        // For S3 URLs
        if (str_contains($url, 's3.amazonaws.com') || str_contains($url, 'amazonaws.com')) {
            $parts = parse_url($url);
            return ltrim($parts['path'], '/');
        }

        // For local URLs
        if (str_contains($url, '/storage/')) {
            return str_replace(url('storage/'), '', $url);
        }

        // Fallback: try to extract path after domain
        $parts = parse_url($url);
        return ltrim($parts['path'] ?? '', '/');
    }

    /**
     * Get file info
     *
     * @param string $filePath
     * @return array|null
     */
    public function getFileInfo(string $filePath): ?array
    {
        try {
            $disk = $this->getStorageDisk();
            $path = $this->extractPathFromUrl($filePath);
            
            if (!Storage::disk($disk)->exists($path)) {
                return null;
            }

            return [
                'path' => $path,
                'url' => $this->getFileUrl($path, $disk),
                'size' => Storage::disk($disk)->size($path),
                'last_modified' => Storage::disk($disk)->lastModified($path),
                'disk' => $disk,
            ];
        } catch (Exception $e) {
            \Log::error('Failed to get file info: ' . $e->getMessage());
            return null;
        }
    }
}
