<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Laravel\Passport\Passport;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot()
    {
        $this->registerPolicies();

        // Configure Passport (migrations are handled manually)

        // Configure Passport token expiration
        $this->configurePassportTokenExpiration();
    }

    /**
     * Configure Passport token expiration settings.
     */
    private function configurePassportTokenExpiration()
    {
        $config = config('passport.token_expiration');

        // Access token expiration
        if ($config['access_token'] !== null && is_numeric($config['access_token'])) {
            Passport::tokensExpireIn(now()->addMinutes((int) $config['access_token']));
        }

        // Refresh token expiration
        if ($config['refresh_token'] !== null && is_numeric($config['refresh_token'])) {
            Passport::refreshTokensExpireIn(now()->addMinutes((int) $config['refresh_token']));
        }

        // Personal access token expiration
        if ($config['personal_access_token'] !== null && is_numeric($config['personal_access_token'])) {
            Passport::personalAccessTokensExpireIn(now()->addMinutes((int) $config['personal_access_token']));
        }
    }
}
