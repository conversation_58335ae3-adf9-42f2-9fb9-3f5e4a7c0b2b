#!/bin/bash

echo "🚀 YAY or NAY API - One Command Docker Setup"
echo "============================================"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

echo "🧹 Cleaning up any existing containers..."
docker-compose down -v 2>/dev/null || true

echo "📋 Setting up environment file..."
if [ ! -f .env ]; then
    cp .env.docker .env
    echo "✅ Environment file created from .env.docker"
else
    echo "⚠️  .env file already exists. Using existing file."
fi

echo "🏗️  Building and starting containers..."
docker-compose up -d --build

echo "⏳ Waiting for services to be ready..."
sleep 15

echo "🧪 Running comprehensive tests..."
chmod +x test-docker-setup.sh
./test-docker-setup.sh

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "📋 Quick Access:"
echo "• API: http://localhost:8000"
echo "• API Documentation: http://localhost:8000"
echo "• Health Check: http://localhost:8000/health"
echo "• phpMyAdmin: http://localhost:8080"
echo ""
echo "🔧 Useful Commands:"
echo "• View logs: docker-compose logs -f app"
echo "• Stop containers: docker-compose down"
echo "• Restart: docker-compose restart"
echo "• Shell access: docker-compose exec app bash"
echo ""
echo "📚 Documentation:"
echo "• API Docs: docs/AUTHENTICATION_API.md"
echo "• Postman Collection: docs/YAY_or_NAY_Auth_APIs.postman_collection.json"
echo "• Test Scripts: docs/test_auth_api.php"
