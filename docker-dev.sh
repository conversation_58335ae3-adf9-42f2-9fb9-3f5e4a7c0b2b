#!/bin/bash

# YAY or NAY API - Development Environment Setup
# Usage: ./docker-dev.sh [dev]
# 
# Without 'dev' parameter: Runs normal production-like setup
# With 'dev' parameter: Includes Mailtrap container for email testing

set -e

ENVIRONMENT=${1:-"prod"}

echo "🚀 Starting YAY or NAY API..."
echo "Environment: $ENVIRONMENT"

if [ "$ENVIRONMENT" = "dev" ]; then
    echo "📧 Including Mailtrap for email testing..."
    echo "Mailtrap Web UI will be available at: http://localhost:8025"
    echo "SMTP settings for development:"
    echo "  Host: mailtrap"
    echo "  Port: 1025"
    echo "  Username: (not required)"
    echo "  Password: (not required)"
    echo ""
    
    # Start with dev profile (includes Mailtrap)
    docker-compose --profile dev up -d
    
    echo "✅ Development environment started!"
    echo ""
    echo "📧 Mailtrap Email Testing:"
    echo "   Web UI: http://localhost:8025"
    echo "   SMTP: localhost:1025 (from host) or mailtrap:1025 (from containers)"
    echo ""
else
    echo "🏭 Starting production-like environment..."
    
    # Start without dev profile (no Mailtrap)
    docker-compose up -d
    
    echo "✅ Production-like environment started!"
    echo ""
fi

echo "🌐 Application URLs:"
echo "   API: http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo "   phpMyAdmin: http://localhost:8080"
echo ""

echo "🔧 Useful Commands:"
echo "   View logs: docker-compose logs -f yay-api"
echo "   Enter container: docker exec -it yay-api bash"
echo "   Stop services: docker-compose down"
if [ "$ENVIRONMENT" = "dev" ]; then
    echo "   Stop with Mailtrap: docker-compose --profile dev down"
fi
echo ""

echo "🧪 Test the API:"
echo "   Registration: curl -X POST http://localhost:8000/api/register -H 'Content-Type: application/json' -d '{\"name\":\"Test User\",\"email\":\"<EMAIL>\",\"password\":\"password123\",\"password_confirmation\":\"password123\"}'"
echo ""

if [ "$ENVIRONMENT" = "dev" ]; then
    echo "📧 To test password reset emails:"
    echo "   1. Register a user"
    echo "   2. Request password reset: curl -X POST http://localhost:8000/api/password/email -H 'Content-Type: application/json' -d '{\"email\":\"<EMAIL>\"}'"
    echo "   3. Check Mailtrap UI at http://localhost:8025 for the reset email"
    echo ""
fi

echo "🎉 Setup complete!"
