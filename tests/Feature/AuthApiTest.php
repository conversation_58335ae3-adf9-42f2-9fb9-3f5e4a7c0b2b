<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthApiTest extends TestCase
{
    use WithFaker;

    /** @test */
    public function test_register_endpoint_validation()
    {
        $response = $this->postJson('/api/register', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'email', 'username', 'password']);
    }

    /** @test */
    public function test_login_endpoint_validation()
    {
        $response = $this->postJson('/api/login', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['login', 'password']);
    }

    /** @test */
    public function test_social_auth_endpoint_validation()
    {
        $response = $this->postJson('/api/social-auth', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['provider', 'access_token']);
    }

    /** @test */
    public function test_forgot_password_endpoint_validation()
    {
        $response = $this->postJson('/api/forgot-password', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function test_reset_password_endpoint_validation()
    {
        $response = $this->postJson('/api/reset-password', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['token', 'email', 'password']);
    }

    /** @test */
    public function test_protected_routes_require_authentication()
    {
        $routes = [
            ['method' => 'post', 'uri' => '/api/logout'],
            ['method' => 'get', 'uri' => '/api/profile'],
            ['method' => 'put', 'uri' => '/api/profile'],
            ['method' => 'post', 'uri' => '/api/change-password'],
        ];

        foreach ($routes as $route) {
            $response = $this->json($route['method'], $route['uri']);
            $response->assertStatus(401);
        }
    }

    /** @test */
    public function test_register_with_valid_data_structure()
    {
        $userData = [
            'name' => 'Test User',
            'email' => 'test' . time() . '@example.com',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/register', $userData);

        // Should return success structure even if there are other issues
        $response->assertJsonStructure([
            'success',
            'data',
            'message'
        ]);
    }

    /** @test */
    public function test_login_with_nonexistent_user()
    {
        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $response = $this->postJson('/api/login', $loginData);

        $response->assertStatus(401);
    }

    /** @test */
    public function test_social_auth_with_invalid_provider()
    {
        $socialData = [
            'provider' => 'invalid_provider',
            'access_token' => 'fake_token',
        ];

        $response = $this->postJson('/api/social-auth', $socialData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['provider']);
    }

    /** @test */
    public function test_change_password_validation()
    {
        $response = $this->postJson('/api/change-password', []);

        $response->assertStatus(401); // Should be unauthorized first
    }

    /** @test */
    public function test_api_routes_exist()
    {
        // Test that all our API routes are registered
        $routes = [
            'POST /api/register',
            'POST /api/login',
            'POST /api/social-auth',
            'POST /api/forgot-password',
            'POST /api/reset-password',
            'POST /api/logout',
            'GET /api/profile',
            'PUT /api/profile',
            'POST /api/change-password',
        ];

        foreach ($routes as $route) {
            [$method, $uri] = explode(' ', $route);
            $response = $this->json($method, $uri);
            
            // Should not return 404 (route not found)
            $this->assertNotEquals(404, $response->getStatusCode(), "Route {$route} not found");
        }
    }
}
