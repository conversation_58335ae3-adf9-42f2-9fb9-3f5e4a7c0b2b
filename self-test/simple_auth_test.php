<?php

// Simple test to check if auth APIs are working
echo "Testing Auth APIs with cURL\n";
echo "===========================\n\n";

$baseUrl = 'http://yay-api.dvl.to/api';

// Test 1: Check if validation works (should return 422)
echo "1. Testing validation (empty registration)...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/register');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Status: $httpCode\n";
if ($httpCode == 422) {
    echo "✅ Validation working!\n";
} else {
    echo "❌ Unexpected status code\n";
    echo "Response: " . substr($response, 0, 200) . "\n";
}

echo "\n";

// Test 2: Check if routes exist
echo "2. Testing route existence...\n";
$routes = [
    '/register' => 'POST',
    '/login' => 'POST', 
    '/social-auth' => 'POST',
    '/forgot-password' => 'POST',
    '/reset-password' => 'POST',
    '/logout' => 'POST',
    '/profile' => 'GET',
];

foreach ($routes as $route => $method) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . $route);
    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
    }
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode == 404) {
        echo "❌ $method $route - Route not found\n";
    } else {
        echo "✅ $method $route - Route exists (Status: $httpCode)\n";
    }
}

echo "\n";

// Test 3: Try registration with valid data
echo "3. Testing registration with valid data...\n";
$userData = [
    'name' => 'Test User ' . time(),
    'email' => 'test' . time() . '@example.com',
    'username' => 'test' . time(),
    'password' => 'password123',
    'password_confirmation' => 'password123'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/register');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($userData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "Status: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}

if ($response) {
    $data = json_decode($response, true);
    if ($httpCode == 200 && isset($data['data']['token'])) {
        echo "✅ Registration successful!\n";
        echo "Token: " . substr($data['data']['token'], 0, 20) . "...\n";
        echo "User: " . $data['data']['user']['name'] . " (" . $data['data']['user']['email'] . ")\n";
    } else {
        echo "❌ Registration failed\n";
        echo "Response: " . substr($response, 0, 300) . "\n";
    }
} else {
    echo "❌ No response received\n";
}

echo "\n";
echo "Test completed!\n";
echo "===============\n";
echo "If registration is failing, check:\n";
echo "1. Database connection\n";
echo "2. Passport clients in oauth_clients table\n";
echo "3. Laravel logs in storage/logs/\n";
