<?php

// Test script to verify token expiration configuration
echo "Testing Token Expiration Configuration\n";
echo "=====================================\n\n";

// Read .env file to get current configuration
function getEnvValue($key) {
    $envFile = __DIR__ . '/.env';
    if (!file_exists($envFile)) {
        return null;
    }

    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, $key . '=') === 0) {
            return substr($line, strlen($key . '='));
        }
    }
    return null;
}

// Check current configuration
echo "1. Current Token Expiration Settings:\n";
echo "   ACCESS_TOKEN_EXPIRE: " . (getEnvValue('PASSPORT_ACCESS_TOKEN_EXPIRE') ?? 'null (never expires)') . " minutes\n";
echo "   REFRESH_TOKEN_EXPIRE: " . (getEnvValue('PASSPORT_REFRESH_TOKEN_EXPIRE') ?? 'null (never expires)') . " minutes\n";
echo "   PERSONAL_ACCESS_TOKEN_EXPIRE: " . (getEnvValue('PASSPORT_PERSONAL_ACCESS_TOKEN_EXPIRE') ?? 'null (never expires)') . " minutes\n\n";

// Convert to human readable
function minutesToHuman($minutes) {
    if ($minutes === null || $minutes === 'null') {
        return 'Never expires';
    }
    
    $minutes = (int) $minutes;
    
    if ($minutes < 60) {
        return $minutes . ' minutes';
    } elseif ($minutes < 1440) {
        $hours = round($minutes / 60, 1);
        return $hours . ' hours';
    } elseif ($minutes < 43200) {
        $days = round($minutes / 1440, 1);
        return $days . ' days';
    } else {
        $years = round($minutes / 525600, 1);
        return $years . ' years';
    }
}

echo "2. Human Readable Expiration Times:\n";
echo "   Access Tokens: " . minutesToHuman(getEnvValue('PASSPORT_ACCESS_TOKEN_EXPIRE')) . "\n";
echo "   Refresh Tokens: " . minutesToHuman(getEnvValue('PASSPORT_REFRESH_TOKEN_EXPIRE')) . "\n";
echo "   Personal Access Tokens: " . minutesToHuman(getEnvValue('PASSPORT_PERSONAL_ACCESS_TOKEN_EXPIRE')) . "\n\n";

// Test token creation and check expiration
echo "3. Testing Token Creation:\n";

$baseUrl = 'http://yay-api.dvl.to/api';

// Create a test user and get token
$userData = [
    'name' => 'Token Test User',
    'email' => 'tokentest' . time() . '@example.com',
    'username' => 'tokentest' . time(),
    'password' => 'password123',
    'password_confirmation' => 'password123'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/register');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($userData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    $data = json_decode($response, true);
    if (isset($data['data']['token'])) {
        $token = $data['data']['token'];
        echo "   ✅ Token created successfully\n";
        echo "   Token: " . substr($token, 0, 30) . "...\n";
        
        // Test token immediately
        echo "\n4. Testing Token Validity:\n";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/profile');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Authorization: Bearer ' . $token
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode == 200) {
            echo "   ✅ Token is valid and working\n";
            
            $profileData = json_decode($response, true);
            if (isset($profileData['data']['user'])) {
                echo "   User: " . $profileData['data']['user']['name'] . "\n";
            }
        } else {
            echo "   ❌ Token validation failed (Status: $httpCode)\n";
        }
        
    } else {
        echo "   ❌ No token received in response\n";
    }
} else {
    echo "   ❌ User registration failed (Status: $httpCode)\n";
}

echo "\n5. Configuration Summary:\n";
echo "   ✅ Token expiration is configurable via environment variables\n";
echo "   ✅ Supports lifetime tokens (set to null)\n";
echo "   ✅ Configuration is documented in TOKEN_CONFIGURATION.md\n";
echo "   ✅ Settings are included in .env.example for deployments\n";

echo "\n6. To Test Token Expiration:\n";
echo "   - Set PASSPORT_ACCESS_TOKEN_EXPIRE=1 (1 minute)\n";
echo "   - Run: php artisan config:clear\n";
echo "   - Create a token and wait 1 minute\n";
echo "   - Test the token - it should be expired\n";

echo "\nToken Configuration Test Complete!\n";
echo "=================================\n";
