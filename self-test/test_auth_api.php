<?php

// Simple test script to verify auth APIs work
echo "Testing YAY or NAY Authentication APIs\n";
echo "=====================================\n\n";

$baseUrl = 'http://localhost:8000/api';

// Test 1: Registration
echo "1. Testing Registration API...\n";
$registrationData = [
    'name' => 'Test User ' . time(),
    'email' => 'test' . time() . '@example.com',
    'password' => 'password123',
    'password_confirmation' => 'password123'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/register');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($registrationData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: $httpCode\n";
if ($response) {
    $data = json_decode($response, true);
    if (isset($data['data']['token'])) {
        echo "✅ Registration successful! Token received.\n";
        $token = $data['data']['token'];
    } else {
        echo "❌ Registration failed or no token received.\n";
        echo "Response: " . substr($response, 0, 200) . "...\n";
    }
} else {
    echo "❌ No response received.\n";
}

echo "\n";

// Test 2: Login (if registration failed, try with existing user)
echo "2. Testing Login API...\n";
$loginData = [
    'email' => $registrationData['email'],
    'password' => 'password123'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: $httpCode\n";
if ($response) {
    $data = json_decode($response, true);
    if (isset($data['data']['token'])) {
        echo "✅ Login successful! Token received.\n";
        $token = $data['data']['token'];
    } else {
        echo "❌ Login failed or no token received.\n";
        echo "Response: " . substr($response, 0, 200) . "...\n";
    }
} else {
    echo "❌ No response received.\n";
}

echo "\n";

// Test 3: Profile (if we have a token)
if (isset($token)) {
    echo "3. Testing Profile API...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . '/profile');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Authorization: Bearer ' . $token
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Status: $httpCode\n";
    if ($response) {
        $data = json_decode($response, true);
        if (isset($data['data']['user'])) {
            echo "✅ Profile retrieved successfully!\n";
            echo "User: " . $data['data']['user']['name'] . " (" . $data['data']['user']['email'] . ")\n";
        } else {
            echo "❌ Profile retrieval failed.\n";
            echo "Response: " . substr($response, 0, 200) . "...\n";
        }
    } else {
        echo "❌ No response received.\n";
    }

    // Test 3b: Profile Update
    echo "\n3b. Testing Profile Update API...\n";
    $profileUpdateData = [
        'name' => 'Updated Test User',
        'username' => 'updateduser' . time(),
        'age' => '25-34',
        'location' => 'Test City, Test Country'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . '/profile');
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($profileUpdateData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: Bearer ' . $token
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Status: $httpCode\n";
    if ($response) {
        $data = json_decode($response, true);
        if (isset($data['data']['user'])) {
            echo "✅ Profile updated successfully!\n";
            echo "Updated User: " . $data['data']['user']['name'] . "\n";
            echo "Age: " . ($data['data']['user']['age'] ?? 'null') . "\n";
            echo "Location: " . ($data['data']['user']['location'] ?? 'null') . "\n";
        } else {
            echo "❌ Profile update failed.\n";
            echo "Response: " . substr($response, 0, 200) . "...\n";
        }
    } else {
        echo "❌ No response received.\n";
    }
} else {
    echo "3. Skipping Profile API test (no token available)\n";
}

echo "\n";

// Test 4: Validation errors
echo "4. Testing Validation (should return 422)...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/register');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([])); // Empty data
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: $httpCode\n";
if ($httpCode == 422) {
    echo "✅ Validation working correctly!\n";
} else {
    echo "❌ Validation not working as expected.\n";
    if ($response) {
        echo "Response: " . substr($response, 0, 200) . "...\n";
    }
}

echo "\n";
echo "API Testing Complete!\n";
echo "=====================\n";
echo "Base URL: $baseUrl\n";
echo "Postman Collection: public/docs/YAY_or_NAY_Auth_APIs.postman_collection.json\n";
