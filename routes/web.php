<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

Route::get('/', function () {
    $apiInfo = [
        'api' => 'YAY or NAY API',
        'version' => '1.0.0',
        'laravel_version' => app()->version(),
        'php_version' => PHP_VERSION,
        'environment' => app()->environment(),
        'timezone' => config('app.timezone'),
        'base_url' => config('app.url'),
        'documentation' => [
            'postman_collection' => url('/docs/YAY_or_NAY_Auth_APIs.postman_collection.json'),
            'api_docs' => 'See docs/AUTHENTICATION_API.md (local development only)'
        ],
        'endpoints' => [
            'authentication' => [
                'register' => [
                    'method' => 'POST',
                    'url' => url('/api/register'),
                    'description' => 'Register a new user'
                ],
                'login' => [
                    'method' => 'POST',
                    'url' => url('/api/login'),
                    'description' => 'Login with email/username and password'
                ],
                'social_auth' => [
                    'method' => 'POST',
                    'url' => url('/api/social-auth'),
                    'description' => 'Authenticate with Google/Apple'
                ],
                'forgot_password' => [
                    'method' => 'POST',
                    'url' => url('/api/forgot-password'),
                    'description' => 'Send password reset email'
                ],
                'reset_password' => [
                    'method' => 'POST',
                    'url' => url('/api/reset-password'),
                    'description' => 'Reset password with token'
                ]
            ],
            'protected' => [
                'logout' => [
                    'method' => 'POST',
                    'url' => url('/api/logout'),
                    'description' => 'Logout user (requires auth)'
                ],
                'profile' => [
                    'method' => 'GET',
                    'url' => url('/api/profile'),
                    'description' => 'Get user profile (requires auth)'
                ],
                'update_profile' => [
                    'method' => 'PUT',
                    'url' => url('/api/profile'),
                    'description' => 'Update user profile (requires auth)'
                ],
                'change_password' => [
                    'method' => 'POST',
                    'url' => url('/api/change-password'),
                    'description' => 'Change password (requires auth)'
                ]
            ],
            'utility' => [
                'health_check' => [
                    'method' => 'GET',
                    'url' => url('/health'),
                    'description' => 'API health status'
                ],
                'api_info' => [
                    'method' => 'GET',
                    'url' => url('/api-info'),
                    'description' => 'Detailed API information'
                ]
            ]
        ],
        'authentication_info' => [
            'type' => 'Bearer Token (JWT)',
            'header' => 'Authorization: Bearer {token}',
            'token_source' => 'Obtain from login, register, or social-auth endpoints'
        ],
        'status' => 'operational',
        'timestamp' => now()->toISOString()
    ];

    // Return JSON for API clients, HTML for browsers
    if (request()->wantsJson() || request()->header('Accept') === 'application/json') {
        return response()->json($apiInfo, 200, [], JSON_PRETTY_PRINT);
    }

    // Simple HTML page for browsers
    $html = '<!DOCTYPE html>
<html>
<head>
    <title>YAY or NAY API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        .info { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .endpoint { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }
        .method { display: inline-block; padding: 2px 8px; border-radius: 3px; color: white; font-size: 12px; font-weight: bold; }
        .post { background: #28a745; }
        .get { background: #007bff; }
        .put { background: #ffc107; color: #000; }
        code { background: #f1f1f1; padding: 2px 5px; border-radius: 3px; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 YAY or NAY API</h1>

        <div class="info">
            <strong>Version:</strong> ' . $apiInfo['version'] . ' |
            <strong>Laravel:</strong> ' . $apiInfo['laravel_version'] . ' |
            <strong>PHP:</strong> ' . $apiInfo['php_version'] . ' |
            <strong>Environment:</strong> ' . $apiInfo['environment'] . '
        </div>

        <h2>📋 Available Endpoints</h2>

        <h3>Authentication (Public)</h3>';

    foreach ($apiInfo['endpoints']['authentication'] as $name => $endpoint) {
        $html .= '<div class="endpoint">
            <span class="method post">' . $endpoint['method'] . '</span>
            <strong>' . str_replace('_', ' ', ucfirst($name)) . '</strong><br>
            <code>' . $endpoint['url'] . '</code><br>
            <small>' . $endpoint['description'] . '</small>
        </div>';
    }

    $html .= '<h3>Protected (Requires Authentication)</h3>';

    foreach ($apiInfo['endpoints']['protected'] as $name => $endpoint) {
        $methodClass = strtolower($endpoint['method']);
        $html .= '<div class="endpoint">
            <span class="method ' . $methodClass . '">' . $endpoint['method'] . '</span>
            <strong>' . str_replace('_', ' ', ucfirst($name)) . '</strong><br>
            <code>' . $endpoint['url'] . '</code><br>
            <small>' . $endpoint['description'] . '</small>
        </div>';
    }

    $html .= '<h2>📚 Documentation</h2>
        <p><a href="' . $apiInfo['documentation']['postman_collection'] . '" target="_blank">📥 Download Postman Collection</a></p>
        <p><a href="/health">🔍 Health Check</a> | <a href="/api-info">ℹ️ API Info (JSON)</a></p>

        <h2>🔐 Authentication</h2>
        <div class="info">
            <strong>Type:</strong> ' . $apiInfo['authentication_info']['type'] . '<br>
            <strong>Header:</strong> <code>' . $apiInfo['authentication_info']['header'] . '</code><br>
            <strong>How to get token:</strong> ' . $apiInfo['authentication_info']['token_source'] . '
        </div>

        <p><small>Generated at: ' . $apiInfo['timestamp'] . '</small></p>
    </div>
</body>
</html>';

    return response($html)->header('Content-Type', 'text/html');
});

// API Health Check
Route::get('/health', function () {
    try {
        // Test database connection
        $dbStatus = 'connected';
        $userCount = \DB::table('users')->count();
    } catch (\Exception $e) {
        $dbStatus = 'disconnected';
        $userCount = 0;
    }

    return response()->json([
        'status' => 'healthy',
        'api' => 'YAY or NAY API',
        'version' => '1.0.0',
        'laravel' => app()->version(),
        'php' => PHP_VERSION,
        'environment' => app()->environment(),
        'database' => [
            'status' => $dbStatus,
            'users_count' => $userCount
        ],
        'services' => [
            'passport' => class_exists('Laravel\Passport\Passport') ? 'installed' : 'not_installed',
            'socialite' => class_exists('Laravel\Socialite\Facades\Socialite') ? 'installed' : 'not_installed'
        ],
        'timestamp' => now()->toISOString(),
        'uptime' => 'API is running'
    ], 200, [], JSON_PRETTY_PRINT);
});

// API Info (JSON only)
Route::get('/api-info', function () {
    return response()->json([
        'api' => 'YAY or NAY API',
        'version' => '1.0.0',
        'laravel_version' => app()->version(),
        'php_version' => PHP_VERSION,
        'environment' => app()->environment(),
        'timezone' => config('app.timezone'),
        'base_url' => config('app.url'),
        'documentation' => [
            'postman_collection' => url('/docs/YAY_or_NAY_Auth_APIs.postman_collection.json'),
            'api_docs' => 'See docs/AUTHENTICATION_API.md (local development only)'
        ],
        'endpoints' => [
            'authentication' => [
                'register' => ['method' => 'POST', 'url' => url('/api/register')],
                'login' => ['method' => 'POST', 'url' => url('/api/login')],
                'social_auth' => ['method' => 'POST', 'url' => url('/api/social-auth')],
                'forgot_password' => ['method' => 'POST', 'url' => url('/api/forgot-password')],
                'reset_password' => ['method' => 'POST', 'url' => url('/api/reset-password')]
            ],
            'protected' => [
                'logout' => ['method' => 'POST', 'url' => url('/api/logout')],
                'profile' => ['method' => 'GET', 'url' => url('/api/profile')],
                'update_profile' => ['method' => 'PUT', 'url' => url('/api/profile')],
                'change_password' => ['method' => 'POST', 'url' => url('/api/change-password')]
            ],
            'utility' => [
                'health_check' => ['method' => 'GET', 'url' => url('/health')],
                'api_info' => ['method' => 'GET', 'url' => url('/api-info')]
            ]
        ],
        'authentication_info' => [
            'type' => 'Bearer Token (JWT)',
            'header' => 'Authorization: Bearer {token}',
            'token_source' => 'Obtain from login, register, or social-auth endpoints'
        ],
        'status' => 'operational',
        'timestamp' => now()->toISOString()
    ], 200, [], JSON_PRETTY_PRINT);
});

// Password Reset Routes (for Laravel's built-in password reset functionality)
Route::get('/password/reset/{token}', function (string $token) {
    return view('auth.reset-password', ['token' => $token]);
})->middleware('guest')->name('password.reset');

Route::post('/password/reset', function (Request $request) {
    $request->validate([
        'token' => 'required',
        'email' => 'required|email',
        'password' => 'required|min:8|confirmed',
    ]);

    $status = Password::reset(
        $request->only('email', 'password', 'password_confirmation', 'token'),
        function ($user, $password) {
            $user->forceFill([
                'password' => Hash::make($password)
            ])->setRememberToken(Str::random(60));

            $user->save();

            event(new PasswordReset($user));
        }
    );

    return $status === Password::PASSWORD_RESET
                ? redirect()->route('login')->with('status', __($status))
                : back()->withErrors(['email' => [__($status)]]);
})->middleware('guest')->name('password.update');
