services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yay-api
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    ports:
      - "8000:80"
    networks:
      - yay-network
    depends_on:
      - db
      - redis
    environment:
      - APP_ENV=local
      - APP_DEBUG=true

  db:
    image: mysql:8.0
    container_name: yay-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: yay
      MYSQL_ROOT_PASSWORD: root
      MYSQL_PASSWORD: password
      MYSQL_USER: yay_user
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    ports:
      - "3307:3306"
    networks:
      - yay-network

  redis:
    image: redis:7-alpine
    container_name: yay-redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    networks:
      - yay-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: yay-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root
    ports:
      - "8080:80"
    networks:
      - yay-network
    depends_on:
      - db

  # Mailtrap for development (optional - use with --profile dev)
  mailtrap:
    image: mailhog/mailhog
    container_name: yay-mailtrap
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - yay-network
    profiles:
      - dev

networks:
  yay-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
