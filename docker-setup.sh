#!/bin/bash

echo "🐳 Setting up YAY or NAY API with Docker..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Copy environment file
if [ ! -f .env ]; then
    echo "📋 Copying Docker environment file..."
    cp .env.docker .env
else
    echo "⚠️  .env file already exists. Skipping copy."
fi

# Build and start containers
echo "🏗️  Building Docker containers..."
docker-compose build

echo "🚀 Starting containers..."
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Install dependencies and setup Laravel
echo "📦 Installing dependencies..."
docker-compose exec app composer install

echo "🔑 Generating application key..."
docker-compose exec app php artisan key:generate

echo "🗄️  Running database migrations..."
docker-compose exec app php artisan migrate

echo "🔐 Installing Laravel Passport..."
docker-compose exec app php artisan passport:install --force

echo "🧹 Clearing caches..."
docker-compose exec app php artisan config:clear
docker-compose exec app php artisan cache:clear
docker-compose exec app php artisan route:clear

echo "✅ Setup complete!"
echo ""
echo "🌐 API is available at: http://localhost:8000"
echo "🗄️  phpMyAdmin is available at: http://localhost:8080"
echo ""
echo "🧪 Running comprehensive tests..."
chmod +x test-docker-setup.sh
./test-docker-setup.sh
echo ""
echo "📊 Container status:"
docker-compose ps
echo ""
echo "🔧 Useful commands:"
echo "  docker-compose logs app     # View application logs"
echo "  docker-compose exec app bash # Access container shell"
echo "  docker-compose down         # Stop containers"
echo "  docker-compose up -d        # Start containers"
