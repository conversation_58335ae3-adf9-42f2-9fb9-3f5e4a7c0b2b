<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('user_role')->default(1)->after('username'); // Default to normal user role
            $table->string('age')->nullable()->after('user_role');
            $table->string('location')->nullable()->after('age');

            // Add indexes for better performance
            $table->index('user_role');
            $table->index('location');

            // Add foreign key constraint
            $table->foreign('user_role')->references('id')->on('roles')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['user_role']);
            $table->dropIndex(['user_role']);
            $table->dropIndex(['location']);
            $table->dropColumn(['user_role', 'age', 'location']);
        });
    }
};
