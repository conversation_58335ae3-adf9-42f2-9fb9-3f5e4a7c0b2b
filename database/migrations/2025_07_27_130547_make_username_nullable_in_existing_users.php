<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Make username nullable and drop unique constraint temporarily
            $table->dropUnique(['username']);
            $table->string('username')->nullable()->change();

            // Add unique constraint back but allow nulls
            $table->unique('username');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Reverse the changes
            $table->dropUnique(['username']);
            $table->string('username')->nullable(false)->change();
            $table->unique('username');
        });
    }
};
