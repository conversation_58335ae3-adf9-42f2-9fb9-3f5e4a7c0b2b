<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Laravel\Passport\Client;
use Illuminate\Support\Str;

class PassportClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Personal Access Client
        Client::updateOrCreate(
            ['id' => 1],
            [
                'name' => 'YAY or NAY Personal Access Client',
                'secret' => Str::random(40),
                'provider' => null,
                'redirect' => 'http://localhost',
                'personal_access_client' => true,
                'password_client' => false,
                'revoked' => false,
            ]
        );

        // Create Password Grant Client
        Client::updateOrCreate(
            ['id' => 2],
            [
                'name' => 'YAY or NAY Password Grant Client',
                'secret' => Str::random(40),
                'provider' => 'users',
                'redirect' => 'http://localhost',
                'personal_access_client' => false,
                'password_client' => true,
                'revoked' => false,
            ]
        );

        // Update the personal access clients table
        \DB::table('oauth_personal_access_clients')->updateOrInsert(
            ['client_id' => 1],
            ['client_id' => 1, 'created_at' => now(), 'updated_at' => now()]
        );

        $this->command->info('Passport clients created successfully!');
    }
}
