<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'id' => 1,
                'name' => 'normal',
                'description' => 'Normal user - default role for all users created from API'
            ],
            [
                'id' => 2,
                'name' => 'admin',
                'description' => 'Admin user - created by super admin'
            ],
            [
                'id' => 3,
                'name' => 'super_admin',
                'description' => 'Super admin - can create other super admins and admins'
            ]
        ];

        foreach ($roles as $role) {
            Role::updateOrCreate(
                ['id' => $role['id']],
                $role
            );
        }
    }
}
