<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Interest;
use Illuminate\Support\Str;

class InterestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $interests = [
            ['name' => 'Food & Dining', 'color' => '#6366f1'],
            ['name' => 'Entertainment', 'color' => '#8b5cf6'],
            ['name' => 'Shopping', 'color' => '#3b82f6'],
            ['name' => 'Travel & Tourism', 'color' => '#06b6d4'],
            ['name' => 'Education', 'color' => '#8b5cf6'],
            ['name' => 'Sports & Recreation', 'color' => '#3b82f6'],
            ['name' => 'Home & Garden', 'color' => '#6366f1'],
            ['name' => 'Beauty & Personal Care', 'color' => '#8b5cf6'],
            ['name' => 'Automotive', 'color' => '#6366f1'],
            ['name' => 'Pets & Animals', 'color' => '#3b82f6'],
            ['name' => 'Technology', 'color' => '#06b6d4'],
            ['name' => 'Health & Wellness', 'color' => '#06b6d4'],
            ['name' => 'Environment', 'color' => '#06b6d4'],
            ['name' => 'Music', 'color' => '#06b6d4'],
            ['name' => 'Movies', 'color' => '#06b6d4'],
            ['name' => 'Books', 'color' => '#06b6d4'],
            ['name' => 'Fashion', 'color' => '#06b6d4'],
            ['name' => 'Art', 'color' => '#06b6d4'],
            ['name' => 'Gaming', 'color' => '#06b6d4'],
        ];

        foreach ($interests as $interest) {
            Interest::updateOrCreate(
                ['slug' => Str::slug($interest['name'])],
                [
                    'name' => $interest['name'],
                    'slug' => Str::slug($interest['name']),
                    'color' => $interest['color']
                ]
            );
        }
    }
}
