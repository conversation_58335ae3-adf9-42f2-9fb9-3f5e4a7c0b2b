<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        // Call your individual seeders in proper order
        $this->call([
            // 1. Passport clients first (required for OAuth functionality)
            PassportClientSeeder::class,

            // 2. Roles (required for users foreign key)
            RoleSeeder::class,

            // 3. Interests
            InterestSeeder::class,

            // 4. Users (depends on roles)
            UserSeeder::class,

            // Note: Other seeders (ProjectSeeder, TimesheetSeeder, etc.) are commented out
            // as they require factories that don't exist yet. Uncomment when needed.
            // ProjectSeeder::class,
            // TimesheetSeeder::class,
            // AttributeSeeder::class,
            // AttributeValueSeeder::class,
        ]);

        $this->command->info('All seeders completed successfully!');
    }
}
